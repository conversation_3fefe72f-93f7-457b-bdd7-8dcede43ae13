<template>
  <Card
    class="relative hover:shadow-sm transition-all duration-200"
    :class="[item.isRecommended ? 'border-blue-500 bg-blue-50' : 'border-neutral-200']"
  >
    <CardContent class="p-2">
      <div class="flex items-center justify-between">
        <!-- 左侧：复选框和名称 -->
        <div class="flex items-center space-x-2 flex-1 min-w-0">
          <Checkbox
            :model-value="item.selected"
            :disabled="isProcessing"
            class="flex-shrink-0"
            @update:model-value="handleSelectionChange"
          />
          <div class="flex-1 min-w-0">
            <p class="text-xs font-medium text-gray-900 truncate" :title="item.name">
              {{ item.name }}
            </p>
          </div>
        </div>

        <!-- 右侧：Badge 和图表图标 -->
        <div class="flex items-center space-x-1 flex-shrink-0 ml-2">
          <Badge variant="outline" :class="getBadgeClass(item.type)" class="text-xs px-1 py-0">
            {{ getTypeLabel(item.type) }}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            class="h-5 w-5 p-0 hover:bg-neutral-100"
            :disabled="isProcessing"
            @click="handleChartClick"
          >
            <LucideIcon name="Edit2" class="h-3 w-3 text-neutral-500" />
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'

// 定义数据集项的类型
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support' | 'all'
  selected: boolean
  isRecommended?: boolean
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

// 定义props
const props = defineProps<{
  item: DatasetItem
  isProcessing?: boolean
}>()

// 定义emit
const emit = defineEmits<{
  'update:selected': [itemId: string, selected: boolean]
  'chart-click': [item: DatasetItem]
}>()

const handleSelectionChange = (selected: boolean) => {
  emit('update:selected', props.item.id, selected)
}

// 处理图表点击
const handleChartClick = () => {
  emit('chart-click', props.item)
}

// 获取类型标签
const getTypeLabel = (type: string): string => {
  const labels = {
    all: '全部',
    train: '训练',
    test: '测试',
    val: '验证',
    support: '支持',
  }
  return labels[type as keyof typeof labels] || type
}

// 获取Badge样式类
const getBadgeClass = (type: string): string => {
  const classes = {
    all: 'border-gray-300 bg-gray-50 text-gray-700',
    train: 'border-green-300 bg-green-50 text-green-700', // 训练集 - 绿色
    test: 'border-violet-300 bg-violet-50 text-violet-700', // 测试集 - 红色
    val: 'border-yellow-300 bg-yellow-50 text-yellow-700', // 验证集 - 黄色
    support: 'border-gray-300 bg-gray-50 text-gray-700', // 支持集 - 灰色
  }
  return classes[type as keyof typeof classes] || classes.all
}
</script>

<style scoped></style>
