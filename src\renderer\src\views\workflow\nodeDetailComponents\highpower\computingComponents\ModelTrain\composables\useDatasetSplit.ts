import { computed, ref, type Ref, shallowRef, watch } from 'vue'

// 定义数据集项的类型
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support' | 'all'
  selected: boolean
  isRecommended?: boolean
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

// 定义数据集数据结构
interface DatasetData {
  allData: DatasetItem[]
  trainData: DatasetItem[]
  testData: DatasetItem[]
  valData: DatasetItem[]
  supportData: DatasetItem[]
}

export function useDatasetSplit(datasetData: Ref<DatasetData>) {
  // 图表弹框相关状态
  const showChartDialog = ref(false)
  const selectedDataset = ref<DatasetItem | undefined>(undefined)

  // 当前选中的tabs（支持多选）
  const selectedTabs = ref(['allData'])

  // tabs配置
  const tabs = [
    { key: 'allData', label: '全部' },
    { key: 'trainData', label: '训练集' },
    { key: 'testData', label: '测试集' },
    { key: 'valData', label: '验证集' },
    { key: 'supportData', label: '支持集' },
  ]

  // 计算属性：检查是否有任何数据
  const hasAnyData = computed(() => {
    return (
      datasetData.value.trainData.length > 0 ||
      datasetData.value.testData.length > 0 ||
      datasetData.value.valData.length > 0 ||
      datasetData.value.supportData.length > 0
    )
  })

  // 使用 shallowRef 缓存合并后的数据，减少深度响应式开销
  const allDataCache = shallowRef<DatasetItem[]>([])

  // 缓存计算结果
  const selectedCountCache = new Map<string, number>()
  const allSelectedCache = new Map<string, boolean>()
  const indeterminateCache = new Map<string, boolean>()

  // 防抖清理缓存，避免频繁清理
  let cacheCleanupTimer: NodeJS.Timeout | null = null
  const debouncedCacheClear = (tabKeys: string[]) => {
    if (cacheCleanupTimer) {
      clearTimeout(cacheCleanupTimer)
    }

    cacheCleanupTimer = setTimeout(() => {
      tabKeys.forEach((key) => {
        selectedCountCache.delete(key)
        allSelectedCache.delete(key)
        indeterminateCache.delete(key)
      })
    }, 50) // 50ms防抖
  }

  // 手动更新缓存，避免频繁重新计算
  const updateAllDataCache = () => {
    allDataCache.value = [
      ...datasetData.value.trainData,
      ...datasetData.value.testData,
      ...datasetData.value.valData,
      ...datasetData.value.supportData,
    ]
    // 清空缓存
    selectedCountCache.clear()
    allSelectedCache.clear()
    indeterminateCache.clear()
  }

  // 监听数据变化，更新缓存
  watch(
    () => [
      datasetData.value.trainData.length,
      datasetData.value.testData.length,
      datasetData.value.valData.length,
      datasetData.value.supportData.length,
    ],
    updateAllDataCache,
    { immediate: true },
  )

  // 获取指定tab的数据项
  const getTabItems = (tabKey: string): DatasetItem[] => {
    if (tabKey === 'allData') {
      return allDataCache.value
    }
    return datasetData.value[tabKey as keyof DatasetData] || []
  }

  // 获取指定tab的总数量
  const getTotalCount = (tabKey: string): number => {
    return getTabItems(tabKey).length
  }

  // 获取指定tab的选中数量 - 优化版本
  const getSelectedCount = (tabKey: string): number => {
    if (selectedCountCache.has(tabKey)) {
      return selectedCountCache.get(tabKey)!
    }

    const items = getTabItems(tabKey)
    // 使用reduce代替filter，减少一次遍历
    const count = items.reduce((acc, item) => acc + (item.selected ? 1 : 0), 0)
    selectedCountCache.set(tabKey, count)
    return count
  }

  // 获取指定tab的标签
  const getTabLabel = (tabKey: string): string => {
    const tab = tabs.find((t) => t.key === tabKey)
    return tab?.label || ''
  }

  // 获取选中tabs的显示标签
  const getSelectedTabsLabel = (): string => {
    if (selectedTabs.value.length === 0) {
      return '选择数据集'
    } else if (selectedTabs.value.length === 1) {
      return getTabLabel(selectedTabs.value[0])
    } else {
      return `已选择 ${selectedTabs.value.length} 个数据集`
    }
  }

  // 处理tab选择（多选）
  const handleTabSelection = (tabKey: string, checked: boolean) => {
    if (checked) {
      if (!selectedTabs.value.includes(tabKey)) {
        selectedTabs.value.push(tabKey)
      }
    } else {
      const index = selectedTabs.value.indexOf(tabKey)
      if (index > -1) {
        selectedTabs.value.splice(index, 1)
      }
    }
  }

  // 检查是否全选 - 优化版本
  const isAllSelected = (tabKey: string): boolean => {
    if (allSelectedCache.has(tabKey)) {
      return allSelectedCache.get(tabKey)!
    }

    const items = getTabItems(tabKey)
    if (items.length === 0) {
      allSelectedCache.set(tabKey, false)
      return false
    }

    // 使用selectedCount缓存，避免重复遍历
    const selectedCount = getSelectedCount(tabKey)
    const allSelected = selectedCount === items.length
    allSelectedCache.set(tabKey, allSelected)
    return allSelected
  }

  // 检查是否半选状态 - 优化版本
  const isIndeterminate = (tabKey: string): boolean => {
    if (indeterminateCache.has(tabKey)) {
      return indeterminateCache.get(tabKey)!
    }

    const items = getTabItems(tabKey)
    const selectedCount = getSelectedCount(tabKey)
    const indeterminate = selectedCount > 0 && selectedCount < items.length
    indeterminateCache.set(tabKey, indeterminate)
    return indeterminate
  }

  // 处理选中项更新 - 优化版本
  const handleUpdateSelectedItems = (tabKey: string, selectedItems: string[]) => {
    const items = getTabItems(tabKey)
    const selectedSet = new Set(selectedItems) // 使用Set提高查找性能

    items.forEach((item) => {
      item.selected = selectedSet.has(item.id)
    })

    // 使用防抖清理缓存
    debouncedCacheClear([tabKey, 'allData'])
  }

  // 批量处理函数，避免频繁的响应式更新
  const batchUpdateItems = async (items: DatasetItem[], selectAll: boolean, batchSize = 100) => {
    const totalItems = items.length

    // 对于小量数据，直接处理
    if (totalItems <= batchSize) {
      items.forEach((item) => {
        item.selected = selectAll
      })
      return
    }

    // 对于大量数据，分批处理
    for (let i = 0; i < totalItems; i += batchSize) {
      const batch = items.slice(i, i + batchSize)
      batch.forEach((item) => {
        item.selected = selectAll
      })

      // 让出控制权，避免阻塞UI
      if (i + batchSize < totalItems) {
        await new Promise((resolve) => setTimeout(resolve, 0))
      }
    }
  }

  // 处理全选/取消全选 - 优化版本
  const handleSelectAll = async (tabKey: string, selectAll: boolean) => {
    try {
      if (tabKey === 'allData') {
        // 优化：批量更新所有数据集
        const dataKeys: (keyof DatasetData)[] = ['trainData', 'testData', 'valData', 'supportData']

        // 并行处理所有数据集
        await Promise.all(
          dataKeys.map(async (key) => {
            await batchUpdateItems(datasetData.value[key], selectAll)
            // 清空相关缓存
            selectedCountCache.delete(key)
            allSelectedCache.delete(key)
            indeterminateCache.delete(key)
          }),
        )
      } else {
        const items = getTabItems(tabKey)
        await batchUpdateItems(items, selectAll)

        // 清空相关缓存
        selectedCountCache.delete(tabKey)
        allSelectedCache.delete(tabKey)
        indeterminateCache.delete(tabKey)
      }

      // 清空allData相关缓存
      selectedCountCache.delete('allData')
      allSelectedCache.delete('allData')
      indeterminateCache.delete('allData')
    } catch (error) {
      console.error('批量更新选择状态时出错:', error)
    }
  }

  // 处理图表点击
  const handleChartClick = (item: DatasetItem) => {
    console.log('点击图表按钮:', item)
    selectedDataset.value = item
    showChartDialog.value = true
  }

  return {
    // 状态
    showChartDialog,
    selectedDataset,
    selectedTabs,
    tabs,

    // 计算属性
    hasAnyData,

    // 方法
    getTabItems,
    getTotalCount,
    getSelectedCount,
    getTabLabel,
    getSelectedTabsLabel,
    handleTabSelection,
    isAllSelected,
    isIndeterminate,
    handleUpdateSelectedItems,
    handleSelectAll,
    handleChartClick,
  }
}
