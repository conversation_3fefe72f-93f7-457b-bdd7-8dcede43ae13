# 任务控制优化说明

## 概述

对 ModelTrain 组件中的任务控制下拉菜单和处理函数进行了优化，使用配置化和循环的方式来减少重复代码，提高可维护性。

## 优化内容

### 1. 任务控制下拉菜单优化

#### 优化前

```vue
<!-- 硬编码的菜单项 -->
<DropdownMenuItem v-if="['Computing', 'Pending', 'Initializing'].includes(trainTaskStatus)">
  <LucideIcon name="CirclePause" class="w-4 h-4 mr-2 text-orange-500" />
  <span class="text-orange-600 font-medium">暂停</span>
</DropdownMenuItem>

<DropdownMenuItem v-if="trainTaskStatus === 'Paused'">
  <LucideIcon name="Play" class="w-4 h-4 mr-2 text-green-500" />
  <span class="text-green-600 font-medium">恢复</span>
</DropdownMenuItem>

<DropdownMenuItem>
  <LucideIcon name="Ban" class="w-4 h-4 mr-2 text-red-500" />
  <span class="text-red-600 font-medium">终止</span>
</DropdownMenuItem>
```

#### 优化后

```vue
<!-- 使用循环展示菜单项 -->
<DropdownMenuItem
  v-for="item in taskControlMenuItems"
  :key="item.key"
  :class="item.hoverColor"
  @click="item.handler"
>
  <LucideIcon :name="item.icon" :class="`w-4 h-4 mr-2 ${item.iconColor}`" />
  <span :class="`${item.textColor} font-medium`">{{ item.label }}</span>
</DropdownMenuItem>
```

#### 配置化菜单项

```typescript
const taskControlMenuItems = computed(() => {
  const items = []

  // 根据任务状态动态生成菜单项
  if (['Computing', 'Pending', 'Initializing'].includes(trainTaskStatus.value)) {
    items.push({
      key: 'pause',
      label: '暂停',
      icon: 'CirclePause',
      iconColor: 'text-orange-500',
      textColor: 'text-orange-600',
      hoverColor: 'hover:bg-orange-50',
      handler: handlePause,
    })
  }

  // ... 其他菜单项

  return items
})
```

### 2. 处理函数优化

#### 优化前

```typescript
// 三个独立的函数，大量重复代码
const handlePause = async () => {
  if (!isTrainTaskProcessing.value) return
  const taskId = state.trainTask.taskId
  if (!taskId) {
    toast.warning('无法暂停', { description: '没有正在进行的任务' })
    return
  }
  try {
    const res = await taskService.pauseTask(taskId)
    if (res.status === 'Success') {
      await taskStore.updateTaskList(taskId)
      toast.success('任务已暂停', { description: '训练任务已暂停' })
      saveCurrentStateImmediate()
    } else {
      toast.error('暂停失败', { description: res.message || '暂停任务时发生错误' })
    }
  } catch (error: any) {
    toast.error('暂停失败', { description: error.message || '暂停任务时发生错误' })
  }
}

// handleStop 和 handleResume 类似...
```

#### 优化后

```typescript
// 配置化的任务操作
const taskOperations = {
  pause: {
    name: '暂停',
    action: (taskId: string) => taskService.pauseTask(taskId),
    successMessage: '任务已暂停',
    successDescription: '训练任务已暂停',
    errorMessage: '暂停失败',
    warningMessage: '无法暂停',
    warningDescription: '没有正在进行的任务',
    requiresProcessing: true,
  },
  // ... 其他操作配置
}

// 通用处理函数
const handleTaskOperation = async (operationType: keyof typeof taskOperations) => {
  const operation = taskOperations[operationType]

  if (operation.requiresProcessing && !isTrainTaskProcessing.value) return

  const taskId = state.trainTask.taskId
  if (!taskId) {
    toast.warning(operation.warningMessage, { description: operation.warningDescription })
    return
  }

  try {
    const result = await operation.action(taskId)
    if (result.status === 'Success') {
      await taskStore.updateTaskList(taskId)
      if (operation.afterSuccess) operation.afterSuccess(taskId)
      toast.success(operation.successMessage, { description: operation.successDescription })
      saveCurrentStateImmediate()
    } else {
      toast.error(operation.errorMessage, {
        description: result.message || `${operation.name}任务时发生错误`,
      })
    }
  } catch (error: any) {
    toast.error(operation.errorMessage, {
      description: error.message || `${operation.name}任务时发生错误`,
    })
  }
}

// 简化的具体函数
const handlePause = () => handleTaskOperation('pause')
const handleStop = () => handleTaskOperation('stop')
const handleResume = () => handleTaskOperation('resume')
```

## 优化优势

### 1. 代码复用

- **减少重复**: 从 ~90 行重复代码减少到 ~30 行配置 + 通用函数
- **统一逻辑**: 所有任务操作使用相同的处理流程

### 2. 可维护性

- **配置化**: 新增任务操作只需添加配置，无需重复编写逻辑
- **集中管理**: 所有任务操作的配置集中在一个地方

### 3. 可扩展性

- **易于扩展**: 添加新的任务操作非常简单
- **灵活配置**: 每个操作可以有不同的行为和提示

### 4. 类型安全

- **TypeScript 支持**: 完整的类型定义，编译时错误检查
- **智能提示**: IDE 可以提供完整的代码补全

## 使用示例

### 添加新的任务操作

```typescript
// 1. 在 taskOperations 中添加配置
const taskOperations = {
  // ... 现有配置
  restart: {
    name: '重启',
    action: (taskId: string) => taskService.restartTask(taskId),
    successMessage: '任务已重启',
    successDescription: '训练任务已成功重启',
    errorMessage: '重启失败',
    warningMessage: '无法重启',
    warningDescription: '没有可重启的任务',
    requiresProcessing: false,
    afterSuccess: (taskId: string) => taskStore.startPolling(taskId),
  },
}

// 2. 添加处理函数
const handleRestart = () => handleTaskOperation('restart')

// 3. 在菜单配置中添加菜单项
const taskControlMenuItems = computed(() => {
  const items = []

  // ... 现有菜单项

  if (someCondition) {
    items.push({
      key: 'restart',
      label: '重启',
      icon: 'RotateCcw',
      iconColor: 'text-blue-500',
      textColor: 'text-blue-600',
      hoverColor: 'hover:bg-blue-50',
      handler: handleRestart,
    })
  }

  return items
})
```

## 性能优化

### 1. 计算属性缓存

- 使用 `computed` 确保菜单项只在依赖变化时重新计算
- 避免不必要的重新渲染

### 2. 条件渲染

- 只在有菜单项时才显示下拉菜单
- 根据任务状态动态显示相关操作

### 3. 事件处理优化

- 避免内联函数，使用预定义的处理器
- 减少组件重新渲染时的函数创建

## 总结

通过配置化和函数式编程的方式，我们成功地：

1. **减少了代码重复**: 从 3 个独立函数合并为 1 个通用函数
2. **提高了可维护性**: 配置化的方式使得修改和扩展更容易
3. **增强了类型安全**: 完整的 TypeScript 类型定义
4. **改善了用户体验**: 统一的交互逻辑和错误处理

这种优化方式可以作为其他类似功能的参考模板。
